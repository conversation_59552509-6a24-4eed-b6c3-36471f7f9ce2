<script setup lang="ts">
import { ref, onMounted } from "vue";
// 使用CSS实现响应式而不是动态绑定
import "./assets/responsive.css";
import { ElMessage } from "element-plus";
import BorderBox from "./components/decorations/BorderBox.vue";
import BackgroundBox from "./components/decorations/BackgroundBox.vue";
import TitleHeader from "./components/decorations/TitleHeader.vue";
import UserUsageSituation from "./components/UserUsageSituation.vue";
import TableTop5 from "./components/TableTop5.vue";
import ResourceCards from "./components/ResourceCards.vue";
import ResourceLoadSituation from "./components/ResourceLoadSituation.vue";
import ResourceUsageChart from "./components/ResourceUsageChart.vue";
import DataCenterMonitor from "./components/DataCenterMonitor.vue";

import DocumentChart from "./components/DocumentChart.vue";
import AlarmOverview from "./components/AlarmOverview.vue";
import AlarmTypePie from "./components/AlarmTypePie.vue";
import AlarmStatistics from "./components/AlarmStatistics.vue";
import AlarmList from "./components/AlarmList.vue";

defineOptions({
  name: "ComprehensiveSituationPresentation"
});

// 页面标题
const title = ref("综合态势呈现");
const currentTime = ref("");

// 机房选择器数据
const selectedDataCenter = ref("");
const dataCenterOptions = ref([
  {
    value: "datacenter1",
    label: "建国西街4楼机房"
  },
  {
    value: "datacenter2",
    label: "朝阳区数据中心"
  },
  {
    value: "datacenter3",
    label: "海淀区机房"
  }
]);

// 响应式布局
// 使用CSS实现响应式而不是动态绑定

// Top5热门功能数据
const topFunctionsData = ref({
  columns: [{ label: "排名" }, { label: "功能名称" }, { label: "访问量" }],
  data: [
    { label: "用户签约信息查询", value: 2267, percent: 100 },
    { label: "5GC视图", value: 1980, percent: 87 },
    { label: "移网IMS性能报表", value: 334, percent: 69 },
    { label: "网络操作备案", value: 273, percent: 66 },
    { label: "5GC性能报表", value: 260, percent: 53 }
  ]
});

// 部门数据
const deptData = ref({
  columns: [{ label: "排名" }, { label: "部门名称" }, { label: "数量" }],
  data: [
    { label: "部门A", value: 2267, percent: 100 },
    { label: "部门B", value: 1980, percent: 87 },
    { label: "部门C", value: 334, percent: 69 },
    { label: "部门D", value: 273, percent: 66 },
    { label: "部门E", value: 260, percent: 53 }
  ]
});

// 用户使用态势数据
const userUsageData = ref({
  rows: [
    {
      id: "user-row",
      icon: "用户数-图标@3x.png",
      items: [
        {
          id: "total-users",
          value: 4369,
          label: "开户用户数",
          unit: "人"
        },
        {
          id: "daily-active-users",
          value: 233,
          label: "日活用户数",
          unit: "人"
        },
        {
          id: "monthly-active-users",
          value: 765,
          label: "月活用户数",
          unit: "人"
        }
      ]
    },
    {
      id: "login-row",
      icon: "登录次数-图标@3x.png",
      items: [
        {
          id: "daily-logins",
          value: 278,
          label: "本日登录次数",
          unit: "次"
        },
        {
          id: "monthly-logins",
          value: 765,
          label: "本月登录次数",
          unit: "次"
        }
      ]
    }
  ]
});

// 公文应用态势数据
const documentUsageData = ref({
  rows: [
    {
      id: "document-processing-row",
      icon: "任务工单-图标@3x.png",
      items: [
        {
          id: "document-processing",
          value: 2355,
          label: "电子公文处理",
          unit: "次"
        }
      ]
    },
    {
      id: "document-seal-row",
      icon: "任务工单-图标@3x(1).png",
      items: [
        {
          id: "document-seal",
          value: 2355,
          label: "电子印章使用",
          unit: "次"
        }
      ]
    },
    {
      id: "document-application-row",
      icon: "任务工单-图标@3x(2).png",
      items: [
        {
          id: "document-application",
          value: 2355,
          label: "电子公文应用",
          unit: "次"
        }
      ]
    }
  ]
});

// 公文应用图表数据
const documentChartData = ref([
  { date: "01", documentProcessing: 150, documentSeal: 100 },
  { date: "03", documentProcessing: 250, documentSeal: 200 },
  { date: "04", documentProcessing: 320, documentSeal: 220 },
  { date: "05", documentProcessing: 350, documentSeal: 300 },
  { date: "06", documentProcessing: 230, documentSeal: 170 },
  { date: "07", documentProcessing: 150, documentSeal: 120 },
  { date: "08", documentProcessing: 280, documentSeal: 240 },
  { date: "09", documentProcessing: 200, documentSeal: 160 },
  { date: "10", documentProcessing: 120, documentSeal: 90 }
]);

// 系统运行态势 - 告警概览数据
const alarmOverviewData = ref([
  {
    id: "total-alarms",
    value: 336,
    label: "总告警数/故障平均时长",
    unit: "min",
    color: "#00a8ff"
  },
  {
    id: "urgent-alarms",
    value: 36,
    label: "紧急告警数/故障平均时长",
    unit: "min",
    color: "#FF3838"
  },
  {
    id: "important-alarms",
    value: 123,
    label: "重要告警数/故障平均时长",
    unit: "min",
    color: "#FF7716"
  }
]);

// 系统运行态势 - 告警类型分布数据
const alarmTypeData = ref([
  { name: "端口告警", value: 25, color: "#FF3838" },
  { name: "ping包告警", value: 20, color: "#FF7716" },
  { name: "不可达告警", value: 18, color: "#E4C513" },
  { name: "网络设备", value: 15, color: "#0095FF" },
  { name: "其它告警", value: 12, color: "#00EAFF" }
]);

// 系统运行态势 - 告警列表数据
const alarmListData = ref([
  {
    id: "01",
    time: "2025-04-14 21:31:49",
    category: "阈值类",
    level: "重要",
    description: "设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%"
  },
  {
    id: "02",
    time: "2025-04-14 21:31:49",
    category: "阈值类",
    level: "严重",
    description: "设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%"
  },
  {
    id: "03",
    time: "2025-04-14 21:31:49",
    category: "阈值类",
    level: "严重",
    description: "设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%"
  },
  {
    id: "04",
    time: "2025-04-14 21:31:49",
    category: "阈值类",
    level: "严重",
    description: "设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%"
  },
  {
    id: "05",
    time: "2025-04-14 21:31:49",
    category: "阈值类",
    level: "严重",
    description: "设备: HKG-CW-r5, 端口: G0/0/0/1的带宽利用率为78%, 超过阈值75%"
  }
]);

// 系统运行态势 - 告警综合关联统计数据
const alarmStatisticsData = ref([
  {
    id: "cert-alarms",
    label: "认证场景",
    value: 25
  },
  {
    id: "network-alarms",
    label: "通讯场景",
    value: 32
  },
  {
    id: "video-conf-alarms",
    label: "视频会议",
    value: 32
  },
  {
    id: "video-monitor-alarms",
    label: "视频监控",
    value: 32
  }
]);

// 导入资源图标
import cpuIcon from "@/views/comprehensiveSituationPresentation/assets/CPU数量-图标@3x.png";
import memoryIcon from "@/views/comprehensiveSituationPresentation/assets/内存容量-图标@3x.png";
import diskIcon from "@/views/comprehensiveSituationPresentation/assets/磁盘容量-图标@3x.png";

// 资源利用率趋势图数据
const resourceUsageChartData = ref([
  {
    id: "cpu-usage-chart",
    // title: '// CPU利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 200 },
      { date: "09-12", value: 300 },
      { date: "09-13", value: 200 },
      { date: "09-14", value: 150 },
      { date: "09-15", value: 200 },
      { date: "09-16", value: 120 },
      { date: "09-17", value: 180 },
      { date: "09-18", value: 250 },
      { date: "09-19", value: 220 },
      { date: "09-20", value: 250 },
      { date: "09-21", value: 330 },
      { date: "09-22", value: 300 }
    ]
  },
  {
    id: "memory-usage-chart",
    // title: '// 内存利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 350 },
      { date: "09-12", value: 220 },
      { date: "09-13", value: 320 },
      { date: "09-14", value: 250 },
      { date: "09-15", value: 300 },
      { date: "09-16", value: 280 },
      { date: "09-17", value: 200 },
      { date: "09-18", value: 120 },
      { date: "09-19", value: 150 },
      { date: "09-20", value: 200 },
      { date: "09-21", value: 300 },
      { date: "09-22", value: 180 }
    ]
  },
  {
    id: "disk-usage-chart",
    // title: '// 磁盘利用率趋势图',
    color: "#0095FF", // 蓝色
    data: [
      { date: "09-11", value: 120 },
      { date: "09-12", value: 180 },
      { date: "09-13", value: 120 },
      { date: "09-14", value: 280 },
      { date: "09-15", value: 250 },
      { date: "09-16", value: 180 },
      { date: "09-17", value: 220 },
      { date: "09-18", value: 250 },
      { date: "09-19", value: 200 },
      { date: "09-20", value: 220 },
      { date: "09-21", value: 300 },
      { date: "09-22", value: 150 }
    ]
  }
]);

// 资源负载态势数据
const resourceLoadData = ref([
  {
    id: "cpu",
    type: "cpu",
    value: 73622,
    percent: 70,
    unit: "",
    label: "CPU数量",
    icon: cpuIcon
  },
  {
    id: "memory",
    type: "memory",
    value: 466091,
    percent: 70,
    unit: "GB",
    label: "内存容量",
    showDecimal: true,
    icon: memoryIcon
  },
  {
    id: "disk",
    type: "disk",
    value: 466091,
    percent: 66,
    unit: "GB",
    label: "磁盘容量",
    showDecimal: true,
    icon: diskIcon
  }
]);

// 加载资源利用率趋势图数据
const loadResourceUsageChartData = async () => {
  try {
    // 这里可以调用实际的API接口
    // const response = await fetch('/api/resource-usage-chart-data');
    // const data = await response.json();
    // resourceUsageChartData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟数据更新
    // 已经在初始化时设置了数据，这里可以进行更新
  } catch (error) {
    console.error("加载资源利用率趋势图数据失败:", error);
    ElMessage.error("加载资源利用率趋势图数据失败");
  }
};

// 加载资源负载态势数据
const loadResourceLoadData = async () => {
  try {
    // 这里可以调用实际的API接口
    // const response = await fetch('/api/resource-load-data');
    // const data = await response.json();
    // resourceLoadData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 模拟数据更新
    resourceLoadData.value = [
      {
        id: "cpu",
        type: "cpu",
        value: 73622,
        percent: 70,
        unit: "",
        label: "CPU数量",
        icon: cpuIcon
      },
      {
        id: "memory",
        type: "memory",
        value: 466091,
        percent: 70,
        unit: "GB",
        label: "内存容量",
        showDecimal: true,
        icon: memoryIcon
      },
      {
        id: "disk",
        type: "disk",
        value: 466091,
        percent: 66,
        unit: "GB",
        label: "磁盘容量",
        showDecimal: true,
        icon: diskIcon
      }
    ];

    // 模拟添加新的资源项
    // 可以根据需要动态添加或删除资源项
    // setTimeout(() => {
    //     resourceLoadData.value.push({
    //         id: 'storage',
    //         type: 'disk',
    //         value: 2048,
    //         percent: 45,
    //         unit: 'TB',
    //         label: '存储容量',
    //         showDecimal: true
    //     });
    // }, 3000);
  } catch (error) {
    console.error("加载资源负载态势数据失败:", error);
    ElMessage.error("加载资源负载态势数据失败");
  }
};

// 加载用户使用态势数据
const loadUserUsageData = async () => {
  try {
    // 这里可以调用实际的API接口
    // const response = await fetch('/api/user-usage-data');
    // const data = await response.json();
    // userUsageData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 可以动态添加或修改数据行
    // 例如：添加一个新的数据行
    // userUsageData.value.rows.push({
    //     id: 'new-row',
    //     icon: 'some-icon.png',
    //     items: [
    //         {
    //             id: 'new-item-1',
    //             value: 100,
    //             label: '新数据项',
    //             unit: '个'
    //         }
    //     ]
    // });

    // ElMessage.success('用户使用态势数据加载成功');
  } catch (error) {
    console.error("加载用户使用态势数据失败:", error);
    ElMessage.error("加载用户使用态势数据失败");
  }
};

// 加载机房监控数据
const loadDataCenterMonitorData = async () => {
  try {
    dataCenterLoading.value = true;
    dataCenterError.value = '';

    console.log('开始加载机房监控数据...');

    // 这里可以调用实际的API接口
    // const response = await fetch('/api/datacenter-monitor-data');
    // const data = await response.json();
    // dataCenterMonitorData.value = data;

    // 模拟数据加载
    await new Promise(resolve => setTimeout(resolve, 500));

    // 确保数据已经设置
    console.log('机房监控数据:', dataCenterMonitorData.value);

    // 模拟实时数据更新
    // 可以定期更新资源使用率数据
    // setInterval(() => {
    //   dataCenterMonitorData.value.resourceUsage.cpu = Math.floor(Math.random() * 100);
    //   dataCenterMonitorData.value.resourceUsage.memory = Math.floor(Math.random() * 100);
    //   dataCenterMonitorData.value.resourceUsage.disk = Math.floor(Math.random() * 100);
    // }, 5000);

    console.log('机房监控数据加载成功');
  } catch (error) {
    console.error("加载机房监控数据失败:", error);
    dataCenterError.value = "加载机房监控数据失败";
    ElMessage.error("加载机房监控数据失败");
  } finally {
    dataCenterLoading.value = false;
  }
};

// 处理时间范围变化
const handleTimeRangeChange = (range: string) => {
  console.log('时间范围切换到:', range);
  dataCenterMonitorData.value.selectedTimeRange = range;
  // 这里可以重新加载对应时间范围的数据
  // loadDataCenterMonitorData();
};

// 处理图表类型变化
const handleChartTypeChange = (type: string) => {
  console.log('图表类型切换到:', type);
  // 这里可以根据需要处理图表类型变化
};

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString();
};

// 处理热门功能数据
const processFunctionData = (functionList: any[]) => {
  if (!functionList || functionList.length === 0) return;

  // 对功能按访问量排序
  const sortedFunctions = [...functionList].sort((a, b) => b.count - a.count);

  // 取前5个功能
  const top5Functions = sortedFunctions.slice(0, 5);

  // 找出最大访问量作为100%基准
  const maxCount = top5Functions.length > 0 ? top5Functions[0].count : 0;

  // 转换为TableTop5组件需要的数据格式
  topFunctionsData.value.data = top5Functions.map(func => ({
    label: func.name,
    value: func.count,
    percent: maxCount > 0 ? Math.round((func.count / maxCount) * 100) : 0
  }));
};

// 模拟热门功能数据
const mockFunctionData = [
  {
    name: "用户签约信息查询",
    id: "function-1",
    count: 2267
  },
  {
    name: "5GC视图",
    id: "function-2",
    count: 1980
  },
  {
    name: "移网IMS性能报表",
    id: "function-3",
    count: 3340
  },
  {
    name: "网络操作备案",
    id: "function-4",
    count: 2730
  },
  {
    name: "5GC性能报表",
    id: "function-5",
    count: 2600
  }
];

// 热门应用TOP5数据
const hotAppsData = ref([
  {
    id: "app-1",
    name: "工单统计",
    count: 2267,
    rank: 1
  },
  {
    id: "app-2",
    name: "消息中心",
    count: 1980,
    rank: 2
  },
  {
    id: "app-3",
    name: "整体态势",
    count: 334,
    rank: 3
  },
  {
    id: "app-4",
    name: "运行态势",
    count: 273,
    rank: 4
  },
  {
    id: "app-5",
    name: "告警查询",
    count: 260,
    rank: 5
  }
]);

// 机房监控数据 - 预留给后续接口对接
const dataCenterMonitorData = ref({
  dataCenterInfo: {
    name: "建国西街4楼机房",
    station: "北京监控局",
    area: "北京丰台区"
  },
  resourceUsage: {
    cpu: 16,
    memory: 16,
    disk: 16
  },
  statistics: {
    cabinets: 101,
    devices: 101,
    alerts: 101
  },
  // 分钟级数据
  minuteData: [
    { date: "14:26", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "14:27", cpu: 7900, memory: 7600, disk: 6100 },
    { date: "14:28", cpu: 8100, memory: 7700, disk: 6000 },
    { date: "14:29", cpu: 8300, memory: 7900, disk: 6300 },
    { date: "14:30", cpu: 8000, memory: 7500, disk: 6000 },
    { date: "14:31", cpu: 7800, memory: 7400, disk: 5900 },
    { date: "14:32", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "14:33", cpu: 8400, memory: 8000, disk: 6400 },
    { date: "14:34", cpu: 8100, memory: 7700, disk: 6100 },
    { date: "14:35", cpu: 7900, memory: 7500, disk: 5800 }
  ],
  // 小时级数据
  hourData: [
    { date: "10:00", cpu: 7500, memory: 7200, disk: 5800 },
    { date: "11:00", cpu: 8000, memory: 7500, disk: 6000 },
    { date: "12:00", cpu: 8200, memory: 7800, disk: 6200 },
    { date: "13:00", cpu: 7800, memory: 7400, disk: 5900 },
    { date: "14:00", cpu: 8100, memory: 7700, disk: 6100 },
    { date: "15:00", cpu: 7900, memory: 7600, disk: 6000 },
    { date: "16:00", cpu: 8300, memory: 7900, disk: 6300 },
    { date: "17:00", cpu: 8000, memory: 7500, disk: 6000 }
  ],
  selectedTimeRange: "分钟"
});

// 机房监控状态
const dataCenterLoading = ref(false);
const dataCenterError = ref('');

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);

  // 加载用户使用态势数据
  loadUserUsageData();

  // 加载资源利用率趋势图数据
  loadResourceUsageChartData();

  // 加载资源负载态势数据
  loadResourceLoadData();

  // 加载机房监控数据
  loadDataCenterMonitorData();

  // 处理热门功能数据
  processFunctionData(mockFunctionData);
});
</script>

<template>
  <background-box class="comprehensive-situation h-full w-full">
    <!-- 顶部标题栏 -->
    <title-header :title="title" />

    <!-- 主体内容区 -->
    <div class="main-content flex h-[calc(100%-80px)]">
      <!-- 左侧系统资源监控区 -->
      <div class="left-panel p-2 w-1/4 ml-2">
        <!-- 资源概览 -->
        <h4>资源概览</h4>
        <div class="resource-overview-container">
          <div class="resource-overview-grid">
            <!-- 顶部卡片区域 -->
            <div class="resource-cards-grid">
              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-blue">
                    <svg viewBox="0 0 24 24" class="card-icon">
                      <path
                        d="M19 20H5V4h14v16zm-3-8h-4v6h-2v-6H8V8h4V6h2v2h4v2z"
                      />
                    </svg>
                  </div>
                  <div class="card-text">
                    <div class="card-value">101</div>
                    <div class="card-label">机房</div>
                  </div>
                </div>
              </div>

              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-green">
                    <svg viewBox="0 0 24 24" class="card-icon">
                      <path
                        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 10h-2v-2h2v2zm7-4h-2v-2h2v2zm0 4h-2v-2h2v2zm-7-4h-2V8h2v4zm-7 4H5v-2h2v2z"
                      />
                    </svg>
                  </div>
                  <div class="card-text">
                    <div class="card-value">386</div>
                    <div class="card-label">机柜</div>
                  </div>
                </div>
              </div>

              <div class="resource-card">
                <div class="card-content">
                  <div class="icon-container icon-cyan">
                    <svg viewBox="0 0 24 24" class="card-icon">
                      <path
                        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 8V7h4v4h-4zm0 4v-2h4v2h-4zm4-8H9v2h6V7zm0 4h-6v-2h6v2z"
                      />
                    </svg>
                  </div>
                  <div class="card-text">
                    <div class="card-value">1,236</div>
                    <div class="card-label">设备</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 中间监控区域 -->
            <div class="resource-load-container">
              <resource-load-situation :items="resourceLoadData" />
            </div>
          </div>
          <div
            style="padding: 10px; display: flex; justify-content: space-between;align-items: center;"
          >
            <h4>资源负载</h4>
            <div style="display: flex; align-items: center;">
                机房:
              <el-select
                v-model="selectedDataCenter"
                clearable
                placeholder="Select"
                style="width: 133px;"
                class="ml-2"
              >
                <el-option
                  v-for="item in dataCenterOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div style="background: #fff;height: 65%;width: 100%;box-sizing: border-box;">
            <data-center-monitor
              :data-center-info="dataCenterMonitorData.dataCenterInfo"
              :resource-usage="dataCenterMonitorData.resourceUsage"
              :statistics="dataCenterMonitorData.statistics"
              :minute-data="dataCenterMonitorData.minuteData"
              :hour-data="dataCenterMonitorData.hourData"
              :selected-time-range="dataCenterMonitorData.selectedTimeRange"
              :loading="dataCenterLoading"
              :error="dataCenterError"
              @time-range-change="handleTimeRangeChange"
              @chart-type-change="handleChartTypeChange"
            />
          </div>
        </div>

        <!-- <border-box :img="'resourceOverview'">
          <resource-cards />
        </border-box> -->

        <!-- 部门公文处理排名 -->
        <!-- <border-box :img="'departmentRanking'">
          <table-top5 :columns="deptData.columns" :data="deptData.data" />
        </border-box> -->

        <!-- 资源负载态势 -->

        <!-- <resource-usage-chart
              :charts="resourceUsageChartData"
              height="133px"
              backgroundColor="#ffffff"
            /> -->
      </div>

      <!-- 中间面板 - 预留给新版本 -->
      <div class="center-panel p-2 flex-1">
        <!-- 这里将在后续版本中添加新的内容 -->
        <div class="placeholder-content">
          <div class="placeholder-text">
            <h3>系统架构区域</h3>
            <p>此区域预留给新版本的系统架构展示</p>
          </div>
        </div>
        <!-- 系统运行态势 -->
        <border-box :img="'systemRunSituation'">
          <div class="system-run-container flex">
            <!-- 左侧告警概览 -->
            <div class="alarm-overview-container w-1/3">
              <alarm-overview :items="alarmOverviewData" />
            </div>

            <!-- 中间告警类型分布饼图 -->
            <div class="alarm-type-container w-1/3">
              <alarm-type-pie :data="alarmTypeData" />
            </div>

            <!-- 右侧告警综合关联统计 -->
            <div class="alarm-statistics-container w-1/3">
              <alarm-statistics :items="alarmStatisticsData" />
            </div>
          </div>

          <!-- 告警列表 -->
          <div class="alarm-list-container w-full mt-2">
            <alarm-list
              :items="alarmListData"
              :default-page-size="5"
              :default-current-page="1"
            />
          </div>
        </border-box>
      </div>

      <!-- 右侧用户及告警统计区 -->
      <div class="right-panel p-2 w-1/4">
        <!-- 用户使用态势 -->
        <border-box>
          <div class="user-data-container">
            <user-usage-situation
              :rows="userUsageData.rows"
              :hot-apps="hotAppsData"
            />
          </div>
        </border-box>

        <!-- 公文应用态势 -->
        <border-box>
          <div class="document-data-container">
            <user-usage-situation
              :isShowMiniTitle="false"
              :rows="documentUsageData.rows"
              :hot-apps="hotAppsData"
              :documentChartData="documentChartData"
              class="document-data-container-user-usage-situation"
            />
            <!-- <div class="document-chart-container">
              <document-chart :chart-data="documentChartData" />
            </div> -->
          </div>
        </border-box>

        <!-- 部门公文处理TOP5 -->
        <!-- <border-box>
          <table-top5 :columns="deptData.columns" :data="deptData.data" />
        </border-box> -->
      </div>
    </div>
  </background-box>
</template>

<style lang="scss" scoped>
.comprehensive-situation {
  width: 100%;
  height: 100%;
  overflow: auto;

  .left-panel {
    background: #fff;
    box-sizing: border-box;
    padding: 10px;
  }

  // 资源概览容器样式
  .resource-overview-container {
    height: 96%;
    width: 100%;
    background: rgb(242, 242, 242);
    box-sizing: border-box;
    padding: 6px;
  }

  .resource-overview-grid {
    display: grid;
    grid-template-rows: auto 1fr;
    gap: 6px;
    padding: 10px;
  }

  .resource-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .resource-card {
    display: grid;
    place-items: center;
    background: #fff;
    border-radius: 5px;
    height: 60px;
  }

  .card-content {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .icon-container {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.icon-blue {
      background: #3a85ff;
    }

    &.icon-green {
      background: #34c759;
    }

    &.icon-cyan {
      background: #3ab9f6;
    }
  }

  .card-icon {
    width: 24px;
    height: 24px;
    fill: white;
  }

  .card-text {
    .card-value {
      font-weight: bold;
      font-size: 16px;
    }

    .card-label {
      color: #666;
      font-size: 13px;
    }
  }

  .resource-load-container {
    display: flex;
    flex-direction: column;
    gap: 2px; // 减少组件之间的间距
    background: #fff;
    padding: 10px;
  }

  .system-run-container {
    min-height: 200px;
    padding: 5px;

    .alarm-overview-container,
    .alarm-type-container,
    .alarm-statistics-container {
      padding: 5px;
      height: 100%;
    }
  }

  .alarm-list-container {
    padding: 5px;
    height: 300px;
  }

  .user-data-container,
  .document-data-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .document-data-container {
    // padding: 10px 0;
    :deep(.data-grid) {
      flex-direction: row;
    }
  }

  .document-chart-container {
    flex: 1;
    // margin-top: 8px;
    background: #ffffff;
    // border: 1px solid #d9e4f5;
    // border-radius: 8px;
    padding: 12px;
    padding-top: 0;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .top5-title {
    font-size: 16px;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 10px;
    padding-left: 5px;
    border-left: 3px solid #1976d2;
  }

  color: #2c3e50;
  margin: 0 !important;
  background: rgba(244, 245, 250, 0.6);
  min-height: 100vh;

  h3 {
    color: #000000d2;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  h4 {
    color: #000000d2;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  :deep(.border-box) {
    position: relative;
    background: #ffffff;
    border-top: none;

    margin-bottom: 8px;

    // 添加左边框渐变
    &::before {
      content: "";
      position: absolute;
      left: -1px;
      top: 0;
      width: 2px;
      height: 100%;
      border-radius: 0 0 0 8px;
    }

    // 添加右边框渐变
    &::after {
      content: "";
      position: absolute;
      right: -1px;
      top: 0;
      width: 2px;
      height: 100%;

      border-radius: 0 0 8px 0;
    }
  }

  .data-cards {
    display: flex;
    justify-content: space-between;

    > div {
      width: 48%;
    }
  }

  .placeholder-content {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border: 1px solid #d9e4f5;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  .placeholder-text {
    text-align: center;
    color: #2c3e50;

    h3 {
      font-size: 24px;
      margin-bottom: 10px;
      color: #1976d2;
    }

    p {
      font-size: 16px;
      opacity: 0.7;
    }
  }

  /* 响应式布局样式 */
  @media screen and (max-width: 1366px) {
    img[style*="height"] {
      transform: scale(0.9);
      transform-origin: left center;
    }

    .operation-bar {
      font-size: 12px;
      gap: 5px;
    }
  }

  @media screen and (max-width: 768px) {
    .system-run-container {
      flex-direction: column !important;

      .alarm-overview-container,
      .alarm-type-container,
      .alarm-statistics-container {
        width: 100% !important;
        margin-bottom: 10px;
      }
    }

    img[style*="height"] {
      transform: scale(0.8);
      transform-origin: left center;
    }
  }

  .operation-bar {
    display: flex;
    gap: 10px;
    padding-right: 10px;
    font-size: 14px;
    color: #2c3e50;
  }

  :deep(.document-data-container-user-usage-situation) {
    .stats-section {
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      margin-bottom: 0;
    }
  }
}
</style>
