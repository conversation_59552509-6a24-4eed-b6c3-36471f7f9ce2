<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

// 定义组件属性
interface DataCenterInfo {
  name: string
  station: string
  area: string
}

interface ResourceUsage {
  cpu: number
  memory: number
  disk: number
}

interface Statistics {
  cabinets: number
  devices: number
  alerts: number
}

interface TrendData {
  date: string
  cpu: number
  memory: number
  disk: number
}

// Props定义
const props = withDefaults(defineProps<{
  dataCenterInfo?: DataCenterInfo
  resourceUsage?: ResourceUsage
  statistics?: Statistics
  minuteData?: TrendData[]
  hourData?: TrendData[]
  selectedTimeRange?: string
  loading?: boolean
  error?: string
}>(), {
  dataCenterInfo: () => ({
    name: '建国西街4楼机房',
    station: '北京监控局',
    area: '北京丰台区'
  }),
  resourceUsage: () => ({
    cpu: 16,
    memory: 16,
    disk: 16
  }),
  statistics: () => ({
    cabinets: 101,
    devices: 101,
    alerts: 101
  }),
  minuteData: () => [
    { date: '14:26', cpu: 8200, memory: 7800, disk: 6200 },
    { date: '14:27', cpu: 7900, memory: 7600, disk: 6100 },
    { date: '14:28', cpu: 8100, memory: 7700, disk: 6000 },
    { date: '14:29', cpu: 8300, memory: 7900, disk: 6300 },
    { date: '14:30', cpu: 8000, memory: 7500, disk: 6000 },
    { date: '14:31', cpu: 7800, memory: 7400, disk: 5900 },
    { date: '14:32', cpu: 8200, memory: 7800, disk: 6200 }
  ],
  hourData: () => [
    { date: '10:00', cpu: 7500, memory: 7200, disk: 5800 },
    { date: '11:00', cpu: 8000, memory: 7500, disk: 6000 },
    { date: '12:00', cpu: 8200, memory: 7800, disk: 6200 },
    { date: '13:00', cpu: 7800, memory: 7400, disk: 5900 },
    { date: '14:00', cpu: 8100, memory: 7700, disk: 6100 },
    { date: '15:00', cpu: 7900, memory: 7600, disk: 6000 }
  ],
  selectedTimeRange: () => '分钟',
  loading: () => false,
  error: () => ''
})

// 响应式数据
const timeRanges = ref(['分钟', '小时'])

// 图表类型选项
const chartTypes = ref([
  { key: 'cpu', label: 'CPU使用率', color: '#4A90E2' },
  { key: 'memory', label: '内存使用率', color: '#50C878' },
  { key: 'disk', label: '磁盘使用率', color: '#00BFFF' }
])

// 当前选中的图表类型
const activeChartType = ref('cpu')

// 当前选中的时间范围
const activeTimeRange = ref(props.selectedTimeRange)

// ECharts实例
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算属性
const maxTrendValue = computed(() => {
  const minuteValues = props.minuteData?.flatMap(item => [item.cpu, item.memory, item.disk]) || []
  const hourValues = props.hourData?.flatMap(item => [item.cpu, item.memory, item.disk]) || []
  const allValues = [...minuteValues, ...hourValues]

  if (allValues.length === 0) return 10000
  return Math.max(...allValues, 1000)
})

// 当前图表数据 - 根据时间范围和图表类型获取对应数据
const currentChartData = computed(() => {
  // 根据时间范围选择数据源
  const dataSource = activeTimeRange.value === '分钟' ? props.minuteData : props.hourData

  if (!dataSource || dataSource.length === 0) return []

  return dataSource.map(item => ({
    date: item.date,
    value: item[activeChartType.value as keyof typeof item] as number
  }))
})

// 当前图表颜色
const currentChartColor = computed(() => {
  const chartType = chartTypes.value.find(type => type.key === activeChartType.value)
  return chartType?.color || '#4A90E2'
})

// ECharts配置
const chartOption = computed(() => {
  const dates = currentChartData.value.map(item => item.date)
  const values = currentChartData.value.map(item => item.value)

  console.log('Chart data:', { dates, values, color: currentChartColor.value })

  if (dates.length === 0 || values.length === 0) {
    console.log('No data available for chart')
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: '#fff',
      borderColor: currentChartColor.value,
      textStyle: {
        color: '#000'
      },
      formatter: function(params: any) {
        const param = params[0]
        return `${param.name}<br/>${param.seriesName}: ${formatValue(param.value)}`
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '13%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e0e0',
          width: 1
        }
      },
      axisTick: {
        show: true,
        length: 4,
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
        margin: 8,
        rotate: 0
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e0e0',
          width: 1
        }
      },
      axisTick: {
        show: true,
        length: 4,
        lineStyle: {
          color: '#e0e0e0'
        }
      },
      axisLabel: {
        color: '#999',
        fontSize: 12,
        margin: 8,
        formatter: (value: number) => formatValue(value)
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f5f5f5',
          width: 1,
          type: 'solid'
        }
      }
    },
    series: [
      {
        name: chartTypes.value.find(t => t.key === activeChartType.value)?.label || '',
        type: 'line',
        data: values,
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          color: currentChartColor.value,
          width: 2
        },
        itemStyle: {
          color: currentChartColor.value
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: currentChartColor.value + '40'
              },
              {
                offset: 1,
                color: currentChartColor.value + '10'
              }
            ]
          }
        }
      }
    ]
  }
})

// 方法
const handleTimeRangeChange = (range: string) => {
  activeTimeRange.value = range
  emit('timeRangeChange', range)
}

const handleTabChange = (tabName: string) => {
  activeChartType.value = tabName
  emit('chartTypeChange', tabName)
  nextTick(() => {
    updateChart()
  })
}

// 初始化图表
const initChart = () => {
  console.log('Attempting to initialize chart...')
  console.log('chartRef.value:', chartRef.value)

  if (!chartRef.value) {
    console.log('chartRef.value is null, retrying in 100ms...')
    setTimeout(() => {
      initChart()
    }, 100)
    return
  }

  if (chartInstance) {
    console.log('Disposing existing chart instance')
    chartInstance.dispose()
  }

  try {
    console.log('Creating new chart instance...')
    chartInstance = echarts.init(chartRef.value)

    // 先设置一个简单的测试配置
    const testOption = {
      grid: {
        left: '1%',
        right: '1%',
        bottom: '2%',
        top: '1%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['5/21', '5/22', '5/23', '5/24', '5/25'],
        axisLine: {
          show: true,
          lineStyle: { color: '#e0e0e0' }
        },
        axisLabel: {
          color: '#999',
          fontSize: 12,
          margin: 8
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: { color: '#e0e0e0' }
        },
        axisLabel: {
          color: '#fff',
          fontSize: 12,
          margin: 8
        },
        splitLine: {
          lineStyle: { color: '#f5f5f5' }
        }
      },
      series: [{
        data: [8000, 6000, 5500, 7500, 8500],
        type: 'line',
        smooth: true,
        lineStyle: { color: '#4A90E2', width: 100 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: '#4A90E240' },
              { offset: 1, color: '#4A90E210' }
            ]
          }
        }
      }]
    }

    chartInstance.setOption(testOption)
    console.log('Test chart set successfully')

    // 然后更新为实际配置
    setTimeout(() => {
      updateChart()
    }, 100)

    console.log('Chart initialized successfully')
  } catch (error) {
    console.error('Failed to initialize chart:', error)
  }
}

// 更新图表
const updateChart = () => {
  if (!chartInstance) {
    console.log('Chart instance not available')
    return
  }

  try {
    const option = chartOption.value
    console.log('Updating chart with option:', option)
    chartInstance.setOption(option, true)
    chartInstance.resize()
  } catch (error) {
    console.error('Failed to update chart:', error)
  }
}

// 格式化数值显示
const formatValue = (value: number) => {
  if (value >= 10000) {
    return (value / 1000).toFixed(0) + 'K'
  }
  return value.toString()
}

// 监听数据变化
watch(() => [props.minuteData, props.hourData], () => {
  console.log('Data changed:', { minuteData: props.minuteData, hourData: props.hourData })
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

watch(() => activeChartType.value, () => {
  console.log('Chart type changed to:', activeChartType.value)
  nextTick(() => {
    updateChart()
  })
})

watch(() => activeTimeRange.value, () => {
  console.log('Time range changed to:', activeTimeRange.value)
  nextTick(() => {
    updateChart()
  })
})

// 监听loading状态变化，当loading结束后初始化图表
watch(() => props.loading, (newLoading) => {
  if (!newLoading && !chartInstance) {
    nextTick(() => {
      initChart()
    })
  }
})

// 组件挂载后初始化图表
onMounted(() => {
  console.log('Component mounted')
  nextTick(() => {
    // 延迟一点时间确保DOM完全渲染
    setTimeout(() => {
      initChart()
    }, 100)
  })
})

// 组件卸载前销毁图表
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})

// 定义事件
const emit = defineEmits<{
  timeRangeChange: [range: string]
  chartTypeChange: [type: string]
}>()
</script>

<template>
  <div class="datacenter-monitor">
    <!-- 顶部信息区域 -->
    <div class="header-info">
      <h3 class="datacenter-name">{{ dataCenterInfo.name }}</h3>
      <div class="location-info">
        <div class="info-item">
          <span class="info-icon">🏢</span>
          <span class="info-label">站点：</span>
          <span class="info-value">{{ dataCenterInfo.station }}</span>
        </div>
        <div class="info-item">
          <span class="info-icon">📍</span>
          <span class="info-label">区域：</span>
          <span class="info-value">{{ dataCenterInfo.area }}</span>
        </div>
      </div>
    </div>

    <!-- 资源利用率环形图 -->
    <div class="resource-usage-section">
      <div class="usage-item">
        <div class="circular-progress cpu">
          <div class="progress-circle">
            <svg viewBox="0 0 100 100" class="progress-svg">
              <circle cx="50" cy="50" r="45" class="progress-bg"/>
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                class="progress-bar"
                :style="{ strokeDashoffset: 283 - (283 * resourceUsage.cpu) / 100 }"
              />
            </svg>
            <div class="progress-text">
              <div class="progress-label">CPU</div>
              <div class="progress-value">{{ resourceUsage.cpu }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="usage-item">
        <div class="circular-progress memory">
          <div class="progress-circle">
            <svg viewBox="0 0 100 100" class="progress-svg">
              <circle cx="50" cy="50" r="45" class="progress-bg"/>
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                class="progress-bar"
                :style="{ strokeDashoffset: 283 - (283 * resourceUsage.memory) / 100 }"
              />
            </svg>
            <div class="progress-text">
              <div class="progress-label">内存</div>
              <div class="progress-value">{{ resourceUsage.memory }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="usage-item">
        <div class="circular-progress disk">
          <div class="progress-circle">
            <svg viewBox="0 0 100 100" class="progress-svg">
              <circle cx="50" cy="50" r="45" class="progress-bg"/>
              <circle 
                cx="50" 
                cy="50" 
                r="45" 
                class="progress-bar"
                :style="{ strokeDashoffset: 283 - (283 * resourceUsage.disk) / 100 }"
              />
            </svg>
            <div class="progress-text">
              <div class="progress-label">磁盘</div>
              <div class="progress-value">{{ resourceUsage.disk }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="statistics-section">
      <div class="stat-card cabinet">
        <div class="stat-icon">🏢</div>
        <div class="stat-value">{{ statistics.cabinets }}</div>
        <div class="stat-label">机柜数</div>
      </div>
      
      <div class="stat-card device">
        <div class="stat-icon">🖥️</div>
        <div class="stat-value">{{ statistics.devices }}</div>
        <div class="stat-label">设备数</div>
      </div>
      
      <div class="stat-card alert">
        <div class="stat-icon">⚠️</div>
        <div class="stat-value">{{ statistics.alerts }}</div>
        <div class="stat-label">异常数</div>
      </div>
    </div>

    <!-- 趋势图表区域 -->
    <div class="trend-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-message">⚠️ {{ error }}</div>
      </div>

      <!-- Tab切换和图表 -->
      <div v-else class="chart-container">
        <el-tabs
          v-model="activeChartType"
          @tab-change="handleTabChange"
          class="chart-tabs"
        >
          <el-tab-pane
            v-for="chartType in chartTypes"
            :key="chartType.key"
            :label="chartType.label"
            :name="chartType.key"
          >
            <!-- Tab内容为空，图表在外面 -->
          </el-tab-pane>
        </el-tabs>

        <!-- 时间范围选择器 - 移动到tab下方 -->
        <div class="time-range-selector">
          <button
            v-for="range in timeRanges"
            :key="range"
            :class="['time-btn', { active: range === activeTimeRange }]"
            @click="handleTimeRangeChange(range)"
          >
            {{ range }}
          </button>
        </div>

        <!-- ECharts图表容器 -->
        <div
          ref="chartRef"
          class="echarts-container"
        ></div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.datacenter-monitor {
  background: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  // 顶部信息区域
  .header-info {
    .datacenter-name {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0 0 6px 0;
    }

    .location-info {
      display: flex;
      gap: 24px;

      .info-item {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;

        .info-icon {
          font-size: 12px;
        }

        .info-label {
          color: #666;
        }

        .info-value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  // 资源利用率环形图
  .resource-usage-section {
    display: flex;
    justify-content: space-around;
    gap: 16px;
    margin: 8px 0;

    .usage-item {
      flex: 1;
      display: flex;
      justify-content: center;
    }

    .circular-progress {
      position: relative;
      width: 80px;
      height: 80px;

      .progress-circle {
        position: relative;
        width: 100%;
        height: 100%;
      }

      .progress-svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
      }

      .progress-bg {
        fill: none;
        stroke: #f0f0f0;
        stroke-width: 6;
      }

      .progress-bar {
        fill: none;
        stroke-width: 6;
        stroke-linecap: round;
        stroke-dasharray: 283;
        transition: stroke-dashoffset 0.3s ease;
      }

      &.cpu .progress-bar {
        stroke: #4A90E2;
      }

      &.memory .progress-bar {
        stroke: #50C878;
      }

      &.disk .progress-bar {
        stroke: #00BFFF;
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;

        .progress-label {
          font-size: 12px;
          color: #666;
          margin-bottom: 2px;
        }

        .progress-value {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  // 统计卡片区域
  .statistics-section {
    display: flex;
    gap: 12px;
    // margin: 8px 0;

    .stat-card {
      flex: 1;
      // padding: 12px;
      border-radius: 8px;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      // gap: 4px;

      &.cabinet {
        background: #E3F2FD;
      }

      &.device {
        background: #E8F5E8;
      }

      &.alert {
        background: #FFEBEE;
      }

      .stat-icon {
        font-size: 16px;
        // margin-bottom: 4px;
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #333;
      }

      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }

  // 趋势图表区域
  .trend-section {
    flex: 1;
    display: flex;
    flex-direction: column;

    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #666;

      .loading-spinner {
        font-size: 14px;
      }
    }

    .error-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #f56c6c;

      .error-message {
        font-size: 14px;
      }
    }

    .chart-container {
      flex: 1;
      min-height: 280px;

      .chart-tabs {
        :deep(.el-tabs__header) {
          margin: 0 0 3px 0;
        }

        :deep(.el-tabs__nav-wrap) {
          &::after {
            display: none;
          }
        }

        :deep(.el-tabs__item) {
          padding: 0 10px;
          font-size: 13px;
          color: #666;
          border-bottom: 2px solid transparent;

          &.is-active {
            color: #4A90E2;
            border-bottom-color: #4A90E2;
          }

          &:hover {
            color: #4A90E2;
          }
        }

        :deep(.el-tabs__active-bar) {
          background-color: #4A90E2;
        }
      }

      .time-range-selector {
        display: flex;
        justify-content: flex-start;
        // gap: 4px;
        margin: 12px 0 0px 0;

        .time-btn {
          padding: 1px 6px;
          margin-right: 3px;
          border: 1px solid #ddd;
          background: #fff;
          border-radius: 4px;
          font-size: 13px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: #f5f5f5;
          }

          &.active {
            background: #4A90E2;
            color: #fff;
            border-color: #4A90E2;
          }
        }
      }

      .echarts-container {
        width: 100%;
        height: 220px;
        background: #fff;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .datacenter-monitor {
    padding: 16px;
    gap: 16px;

    .location-info {
      flex-direction: column;
      gap: 8px;
    }

    .resource-usage-section {
      .circular-progress {
        width: 60px;
        height: 60px;
      }
    }

    .statistics-section {
      .stat-card {
        padding: 8px;

        .stat-value {
          font-size: 20px;
        }
      }
    }

    .trend-header {
      flex-direction: column;
      gap: 8px;
      align-items: flex-start;
    }

    .trend-section {
      .chart-container {
        min-height: 250px;

        .echarts-container {
          height: 200px;
        }
      }
    }
  }
}
</style>
